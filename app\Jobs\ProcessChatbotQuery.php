<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * Job pour traiter les requêtes chatbot en arrière-plan
 * Améliore les performances en déchargeant le traitement
 */
class ProcessChatbotQuery implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Timeout du job (30 secondes)
     */
    public $timeout = 30;

    /**
     * Nombre de tentatives
     */
    public $tries = 2;

    /**
     * Question à traiter
     */
    protected string $question;

    /**
     * ID de session pour identifier la réponse
     */
    protected string $sessionId;

    /**
     * URL de l'API chatbot
     */
    private const CHATBOT_API_URL = 'http://localhost:8001';

    /**
     * Créer une nouvelle instance du job
     */
    public function __construct(string $question, string $sessionId)
    {
        $this->question = $question;
        $this->sessionId = $sessionId;
    }

    /**
     * Exécuter le job
     */
    public function handle(): void
    {
        try {
            Log::info("Traitement en arrière-plan de la question: {$this->question}");

            // Vérifier si la réponse est déjà en cache
            $cacheKey = 'chatbot_response_' . md5(strtolower(trim($this->question)));
            $cachedResponse = Cache::get($cacheKey);

            if ($cachedResponse !== null) {
                Log::info('Réponse trouvée en cache');
                $this->storeResult($cachedResponse);
                return;
            }

            // Appeler l'API chatbot
            $response = Http::timeout(25)
                ->retry(2, 1000)
                ->post(self::CHATBOT_API_URL . '/query', [
                    'question' => $this->question
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                $responseData = [
                    'status' => 'success',
                    'question' => $this->question,
                    'answer' => $data['response'] ?? 'Réponse vide',
                    'timestamp' => now()->toISOString()
                ];

                // Mettre en cache (10 minutes)
                Cache::put($cacheKey, $responseData, 600);

                // Stocker le résultat pour la session
                $this->storeResult($responseData);

                Log::info('Réponse chatbot traitée avec succès');
            } else {
                $this->handleError('Erreur API: ' . $response->status());
            }

        } catch (\Exception $e) {
            Log::error('Erreur lors du traitement chatbot: ' . $e->getMessage());
            $this->handleError($e->getMessage());
        }
    }

    /**
     * Stocker le résultat pour la session
     */
    private function storeResult(array $responseData): void
    {
        $resultKey = "chatbot_result_{$this->sessionId}";
        Cache::put($resultKey, $responseData, 300); // 5 minutes
    }

    /**
     * Gérer les erreurs
     */
    private function handleError(string $errorMessage): void
    {
        $errorData = [
            'status' => 'error',
            'message' => 'Erreur lors du traitement: ' . $errorMessage,
            'timestamp' => now()->toISOString()
        ];

        $this->storeResult($errorData);
    }

    /**
     * Gérer l'échec du job
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Job chatbot échoué: ' . $exception->getMessage());
        
        $this->handleError('Le traitement a échoué après plusieurs tentatives');
    }
}
