<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Configuration du Chatbot COBIT
    |--------------------------------------------------------------------------
    |
    | Configuration des paramètres de performance et d'optimisation
    | pour le chatbot COBIT intégré.
    |
    */

    // Configuration de l'API FastAPI
    'api' => [
        'url' => env('CHATBOT_API_URL', 'http://localhost:8001'),
        'timeout' => env('CHATBOT_TIMEOUT', 30),
        'health_timeout' => env('CHATBOT_HEALTH_TIMEOUT', 5),
        'retry_attempts' => env('CHATBOT_RETRY_ATTEMPTS', 2),
        'retry_delay' => env('CHATBOT_RETRY_DELAY', 1000), // millisecondes
    ],

    // Configuration du cache
    'cache' => [
        'enabled' => env('CHATBOT_CACHE_ENABLED', true),
        'ttl' => [
            'responses' => env('CHATBOT_CACHE_RESPONSES_TTL', 600), // 10 minutes
            'health' => env('CHATBOT_CACHE_HEALTH_TTL', 30), // 30 secondes
            'suggestions' => env('CHATBOT_CACHE_SUGGESTIONS_TTL', 3600), // 1 heure
        ],
        'prefix' => env('CHATBOT_CACHE_PREFIX', 'chatbot_'),
    ],

    // Configuration des performances
    'performance' => [
        'debounce_interval' => env('CHATBOT_DEBOUNCE_INTERVAL', 1000), // millisecondes
        'max_concurrent_requests' => env('CHATBOT_MAX_CONCURRENT', 5),
        'queue_enabled' => env('CHATBOT_QUEUE_ENABLED', false),
        'async_processing' => env('CHATBOT_ASYNC_PROCESSING', false),
    ],

    // Configuration Ollama
    'ollama' => [
        'host' => env('OLLAMA_HOST', 'http://localhost:11434'),
        'model' => env('OLLAMA_MODEL', 'llama3.1:8b'),
        'timeout' => env('OLLAMA_TIMEOUT', 15),
        'cache_ttl' => env('OLLAMA_CACHE_TTL', 3600), // 1 heure
        'options' => [
            'temperature' => env('OLLAMA_TEMPERATURE', 0.1),
            'top_p' => env('OLLAMA_TOP_P', 0.7),
            'max_tokens' => env('OLLAMA_MAX_TOKENS', 500),
            'top_k' => env('OLLAMA_TOP_K', 20),
            'repeat_penalty' => env('OLLAMA_REPEAT_PENALTY', 1.1),
        ],
    ],

    // Configuration du frontend
    'frontend' => [
        'auto_open' => env('CHATBOT_AUTO_OPEN', false),
        'show_typing_indicator' => env('CHATBOT_SHOW_TYPING', true),
        'local_cache_enabled' => env('CHATBOT_LOCAL_CACHE', true),
        'local_cache_ttl' => env('CHATBOT_LOCAL_CACHE_TTL', 30), // secondes
    ],

    // Messages par défaut
    'messages' => [
        'welcome' => 'Bonjour ! Je suis votre assistant COBIT 2019. Comment puis-je vous aider ?',
        'error' => 'Désolé, une erreur s\'est produite. Veuillez réessayer.',
        'timeout' => 'Le traitement prend plus de temps que prévu. Veuillez patienter...',
        'unavailable' => 'Le chatbot n\'est pas disponible actuellement. Veuillez réessayer plus tard.',
        'rate_limit' => 'Veuillez patienter un moment avant d\'envoyer un autre message.',
    ],

    // Logging
    'logging' => [
        'enabled' => env('CHATBOT_LOGGING_ENABLED', true),
        'level' => env('CHATBOT_LOG_LEVEL', 'info'),
        'log_requests' => env('CHATBOT_LOG_REQUESTS', true),
        'log_responses' => env('CHATBOT_LOG_RESPONSES', false), // Sensible data
    ],
];
