<?php $__env->startSection('title', 'COBIT 2019 - Évaluation'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/cobit-enhanced.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="<?php echo e(asset('js/cobit-evaluation.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="fade-in">
    <!-- Header avec navigation par onglets -->
    <div class="card mb-6">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Évaluation COBIT 2019</h1>
                    <p class="text-gray-600 mt-1">Design Factors - Facteurs de conception</p>
                </div>
                <div class="flex space-x-3">
                    <button id="save-all-btn" class="btn btn-success">
                        <i class="fas fa-save mr-2"></i>Sauvegarder Tout
                    </button>
                    <button id="export-btn" class="btn btn-secondary">
                        <i class="fas fa-download mr-2"></i>Exporter
                    </button>
                    <button id="reset-all-btn" class="btn btn-danger">
                        <i class="fas fa-redo mr-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Onglets des Design Factors -->
        <div class="p-4 border-b">
            <div class="flex flex-wrap gap-2">
                <?php $__currentLoopData = $designFactors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $df): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button
                    id="tab-df<?php echo e($df->getNumberFromCode()); ?>"
                    data-df="<?php echo e($df->getNumberFromCode()); ?>"
                    class="df-tab relative px-4 py-2 rounded-lg font-medium transition-all <?php echo e($df->getNumberFromCode() == 1 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'); ?>">
                    <?php echo e($df->code); ?>

                    <span class="ml-2 text-xs opacity-75"><?php echo e($df->title); ?></span>
                    <!-- Indicateur de validation -->
                    <span class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white" 
                          id="status-indicator-df<?php echo e($df->getNumberFromCode()); ?>"
                          style="background-color: #6B7280;"></span>
                </button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                
                <!-- Onglet Résultats -->
                <button
                    id="tab-results"
                    class="df-tab px-4 py-2 rounded-lg font-medium transition-all bg-green-100 text-green-700 hover:bg-green-200 disabled:opacity-50"
                    disabled>
                    <i class="fas fa-chart-bar mr-2"></i>Résultats Finaux
                    <span class="ml-2 text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full" id="completed-count">0/10</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Contenu des Design Factors -->
    <?php $__currentLoopData = $designFactors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $df): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div id="df<?php echo e($df->getNumberFromCode()); ?>-content" class="df-content" style="<?php echo e($df->getNumberFromCode() == 1 ? 'display: block;' : 'display: none;'); ?>">
        <div class="card mb-6">
            <!-- En-tête du DF -->
            <div class="card-header">
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900"><?php echo e($df->title); ?></h2>
                        <p class="text-gray-600 mt-1"><?php echo e($df->description); ?></p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-500"><?php echo e($df->code); ?></span>
                        <button data-df="<?php echo e($df->getNumberFromCode()); ?>" class="btn btn-secondary btn-sm reset-df-btn">
                            <i class="fas fa-undo mr-1"></i>Reset
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Section des paramètres -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-sliders-h mr-2 text-blue-600"></i>
                            Paramètres d'évaluation
                        </h3>
                        
                        <!-- Paramètres d'entrée -->
                        <div class="space-y-6">
                            <?php $__currentLoopData = $df->parameters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $param): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $min = $param['min'] ?? 0;
                                $max = $param['max'] ?? 5;
                                $default = $param['default'] ?? 0;
                            ?>
                            <div class="parameter-group">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <?php echo e($param['label']); ?>

                                    <span class="text-blue-600 font-bold ml-2" id="value-df<?php echo e($df->getNumberFromCode()); ?>-<?php echo e($index); ?>"><?php echo e($default); ?></span>
                                </label>
                                <input 
                                    type="range" 
                                    min="<?php echo e($min); ?>" 
                                    max="<?php echo e($max); ?>" 
                                    value="<?php echo e($default); ?>"
                                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                    id="input-df<?php echo e($df->getNumberFromCode()); ?>-<?php echo e($index); ?>"
                                    data-df="<?php echo e($df->getNumberFromCode()); ?>"
                                    data-index="<?php echo e($index); ?>"
                                >
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span><?php echo e($min); ?></span>
                                    <span><?php echo e($max); ?></span>
                                </div>
                                <p class="text-xs text-gray-600 mt-1"><?php echo e($param['description'] ?? ''); ?></p>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Actions pour ce DF -->
                        <div class="mt-8 flex space-x-4">
                            <button data-df="<?php echo e($df->getNumberFromCode()); ?>" class="btn btn-success save-df-btn">
                                <i class="fas fa-save mr-2"></i>Sauvegarder <?php echo e($df->code); ?>

                            </button>
                            <button data-df="<?php echo e($df->getNumberFromCode()); ?>" class="btn btn-secondary reset-df-btn">
                                <i class="fas fa-undo mr-2"></i>Réinitialiser
                            </button>
                        </div>
                    </div>

                    <!-- Section des résultats et graphiques -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">
                            <i class="fas fa-chart-line mr-2 text-green-600"></i>
                            Résultats et Visualisation
                        </h3>
                        
                        <!-- Tableau des objectifs spécifiques au DF -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-table mr-2 text-indigo-600"></i>
                                    Objectifs Impactés par <?php echo e($df->code); ?>

                                    <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full" id="objectives-count-df<?php echo e($df->getNumberFromCode()); ?>">0</span>
                                </h4>
                                <div class="flex space-x-2 mt-2">
                                    <button data-df="<?php echo e($df->getNumberFromCode()); ?>" data-sort="score" class="btn btn-xs btn-secondary sort-objectives-btn">
                                        <i class="fas fa-sort-numeric-down mr-1"></i>Score
                                    </button>
                                    <button data-df="<?php echo e($df->getNumberFromCode()); ?>" data-sort="ri" class="btn btn-xs btn-secondary sort-objectives-btn">
                                        <i class="fas fa-sort-amount-down mr-1"></i>RI
                                    </button>
                                    <button data-df="<?php echo e($df->getNumberFromCode()); ?>" data-sort="gap" class="btn btn-xs btn-secondary sort-objectives-btn">
                                        <i class="fas fa-sort-numeric-down mr-1"></i>Écart
                                    </button>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="overflow-x-auto max-h-64">
                                    <table class="min-w-full divide-y divide-gray-200 text-sm">
                                        <thead class="bg-gray-50 sticky top-0">
                                            <tr>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Objectif</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Domaine</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Score</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Baseline</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">RI</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Écart</th>
                                                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">Impact</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200" id="objectives-table-df<?php echo e($df->getNumberFromCode()); ?>">
                                            <!-- Les objectifs seront injectés ici par JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- IA Bundle Simple -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-brain mr-2 text-purple-600"></i>
                                    IA Bundle - Analyse Intelligente
                                </h4>
                            </div>
                            <div class="p-4">
                                <div class="space-y-3">
                                    <div class="bg-gradient-to-r from-purple-50 to-blue-50 p-3 rounded-lg">
                                        <div class="text-sm font-medium text-purple-800">Recommandation IA</div>
                                        <div class="text-sm text-purple-700 mt-1" id="ai-recommendation-df<?php echo e($df->getNumberFromCode()); ?>">
                                            Analysez vos paramètres pour obtenir des recommandations...
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-3 gap-2">
                                        <div class="text-center p-2 bg-green-50 rounded">
                                            <div class="text-lg font-bold text-green-600" id="ai-score-df<?php echo e($df->getNumberFromCode()); ?>">-</div>
                                            <div class="text-xs text-gray-600">Score IA</div>
                                        </div>
                                        <div class="text-center p-2 bg-yellow-50 rounded">
                                            <div class="text-lg font-bold text-yellow-600" id="ai-risk-df<?php echo e($df->getNumberFromCode()); ?>">-</div>
                                            <div class="text-xs text-gray-600">Niveau Risque</div>
                                        </div>
                                        <div class="text-center p-2 bg-blue-50 rounded">
                                            <div class="text-lg font-bold text-blue-600" id="ai-priority-df<?php echo e($df->getNumberFromCode()); ?>">-</div>
                                            <div class="text-xs text-gray-600">Priorité IA</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Statistiques en temps réel -->
                        <div class="card">
                            <div class="card-header">
                                <h4 class="text-md font-semibold flex items-center">
                                    <i class="fas fa-calculator mr-2 text-orange-600"></i>
                                    Métriques <?php echo e($df->code); ?>

                                </h4>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                                        <div class="text-2xl font-bold text-blue-600" id="avg-score-df<?php echo e($df->getNumberFromCode()); ?>">0.0</div>
                                        <div class="text-xs text-gray-600">Score Moyen</div>
                                    </div>
                                    <div class="text-center p-3 bg-green-50 rounded-lg">
                                        <div class="text-2xl font-bold text-green-600" id="max-impact-df<?php echo e($df->getNumberFromCode()); ?>">0</div>
                                        <div class="text-xs text-gray-600">Impact Max</div>
                                    </div>
                                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                                        <div class="text-2xl font-bold text-purple-600" id="affected-objectives-df<?php echo e($df->getNumberFromCode()); ?>">0</div>
                                        <div class="text-xs text-gray-600">Objectifs Affectés</div>
                                    </div>
                                    <div class="text-center p-3 bg-orange-50 rounded-lg">
                                        <div class="text-2xl font-bold text-orange-600" id="completion-df<?php echo e($df->getNumberFromCode()); ?>">0%</div>
                                        <div class="text-xs text-gray-600">Complétude</div>
                                    </div>
                                </div>
                                
                                <!-- Indicateur de validation -->
                                <div class="mt-4 text-center">
                                    <div class="inline-flex items-center px-4 py-2 rounded-full" id="validation-status-df<?php echo e($df->getNumberFromCode()); ?>">
                                        <i class="fas fa-clock mr-2"></i>
                                        <span>En attente de validation</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Graphiques -->
                        <div class="grid grid-cols-1 gap-4 mt-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="text-md font-semibold">Graphique en Barres</h4>
                                </div>
                                <div class="p-4">
                                    <canvas id="bar-chart-df<?php echo e($df->getNumberFromCode()); ?>" width="400" height="200"></canvas>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="text-md font-semibold">Graphique Radar</h4>
                                </div>
                                <div class="p-4">
                                    <canvas id="radar-chart-df<?php echo e($df->getNumberFromCode()); ?>" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<!-- Meta token pour CSRF -->
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\symfcopite\symf\symfcobite\cobit-laravel\resources\views\cobit\evaluation-simple.blade.php ENDPATH**/ ?>