# Configuration optimisée pour le chatbot COBIT
# Copiez ces variables dans votre fichier .env pour améliorer les performances

# Configuration de l'API Chatbot
CHATBOT_API_URL=http://localhost:8001
CHATBOT_TIMEOUT=30
CHATBOT_HEALTH_TIMEOUT=5
CHATBOT_RETRY_ATTEMPTS=2
CHATBOT_RETRY_DELAY=1000

# Configuration du cache (recommandé: Redis pour de meilleures performances)
CACHE_DRIVER=redis
CHATBOT_CACHE_ENABLED=true
CHATBOT_CACHE_RESPONSES_TTL=600
CHATBOT_CACHE_HEALTH_TTL=30
CHATBOT_CACHE_SUGGESTIONS_TTL=3600
CHATBOT_CACHE_PREFIX=chatbot_

# Configuration des performances
CHATBOT_DEBOUNCE_INTERVAL=1000
CHATBOT_MAX_CONCURRENT=5
CHATBOT_QUEUE_ENABLED=true
CHATBOT_ASYNC_PROCESSING=true

# Configuration Ollama (optimisée pour la vitesse)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b
OLLAMA_TIMEOUT=15
OLLAMA_CACHE_TTL=3600
OLLAMA_TEMPERATURE=0.1
OLLAMA_TOP_P=0.7
OLLAMA_MAX_TOKENS=500
OLLAMA_TOP_K=20
OLLAMA_REPEAT_PENALTY=1.1

# Configuration du frontend
CHATBOT_AUTO_OPEN=false
CHATBOT_SHOW_TYPING=true
CHATBOT_LOCAL_CACHE=true
CHATBOT_LOCAL_CACHE_TTL=30

# Configuration des queues (pour le traitement asynchrone)
QUEUE_CONNECTION=redis

# Configuration Redis (pour de meilleures performances)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# Configuration de logging
CHATBOT_LOGGING_ENABLED=true
CHATBOT_LOG_LEVEL=info
CHATBOT_LOG_REQUESTS=true
CHATBOT_LOG_RESPONSES=false
