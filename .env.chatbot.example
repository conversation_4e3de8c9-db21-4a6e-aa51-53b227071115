# Configuration optimisée pour le chatbot COBIT
# Copiez ces variables dans votre fichier .env pour améliorer les performances

# Configuration de l'API Chatbot (ULTRA-RAPIDE - 5s max)
CHATBOT_API_URL=http://localhost:8001
CHATBOT_TIMEOUT=5
CHATBOT_HEALTH_TIMEOUT=3
CHATBOT_RETRY_ATTEMPTS=1
CHATBOT_RETRY_DELAY=500

# Configuration du cache (recommandé: Redis pour de meilleures performances)
CACHE_DRIVER=redis
CHATBOT_CACHE_ENABLED=true
CHATBOT_CACHE_RESPONSES_TTL=600
CHATBOT_CACHE_HEALTH_TTL=30
CHATBOT_CACHE_SUGGESTIONS_TTL=3600
CHATBOT_CACHE_PREFIX=chatbot_

# Configuration des performances
CHATBOT_DEBOUNCE_INTERVAL=1000
CHATBOT_MAX_CONCURRENT=5
CHATBOT_QUEUE_ENABLED=true
CHATBOT_ASYNC_PROCESSING=true

# Configuration Ollama (ULTRA-OPTIMISÉE pour vitesse maximale)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b
OLLAMA_TIMEOUT=5
OLLAMA_CACHE_TTL=3600
OLLAMA_TEMPERATURE=0.05
OLLAMA_TOP_P=0.5
OLLAMA_MAX_TOKENS=200
OLLAMA_TOP_K=10
OLLAMA_REPEAT_PENALTY=1.0
OLLAMA_NUM_CTX=1024
OLLAMA_NUM_THREAD=4

# Configuration du frontend
CHATBOT_AUTO_OPEN=false
CHATBOT_SHOW_TYPING=true
CHATBOT_LOCAL_CACHE=true
CHATBOT_LOCAL_CACHE_TTL=30

# Configuration des queues (pour le traitement asynchrone)
QUEUE_CONNECTION=redis

# Configuration Redis (pour de meilleures performances)
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1

# Configuration de logging
CHATBOT_LOGGING_ENABLED=true
CHATBOT_LOG_LEVEL=info
CHATBOT_LOG_REQUESTS=true
CHATBOT_LOG_RESPONSES=false
