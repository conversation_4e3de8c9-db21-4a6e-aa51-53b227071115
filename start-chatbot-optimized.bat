@echo off
echo ========================================
echo   CHATBOT COBIT ULTRA-RAPIDE (5s max)
echo ========================================
echo.

echo [1/4] Verification des services...
echo Verification d'Ollama...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Ollama n'est pas demarre !
    echo Demarrez Ollama avec: ollama serve
    pause
    exit /b 1
)

echo Verification du chatbot FastAPI...
curl -s http://localhost:8001/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ERREUR: Chatbot FastAPI n'est pas demarre !
    echo Demarrez le chatbot FastAPI sur le port 8001
    pause
    exit /b 1
)

echo [2/4] Nettoyage du cache...
php artisan config:clear

echo [3/4] Creation utilisateur de test...
php artisan tinker --execute="App\Models\User::firstOrCreate(['email' => '<EMAIL>'], ['name' => 'Test User', 'password' => bcrypt('password')]);"

echo [4/4] Demarrage du serveur Laravel...
echo.
echo ========================================
echo   CHATBOT COBIT PRET !
echo ========================================
echo.
echo Interface web: http://localhost:8000
echo Login: <EMAIL>
echo Password: password
echo.
echo OPTIMISATIONS APPLIQUEES:
echo - Timeout chatbot: 5 secondes maximum
echo - Cache intelligent active
echo - Graphiques corriges (Evaluation des Risques)
echo - Scores limites a 100 maximum
echo.
echo Appuyez sur Ctrl+C pour arreter
echo ========================================
echo.

php artisan serve --host=0.0.0.0 --port=8000
