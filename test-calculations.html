<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Calculs COBIT 2019</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Test des Calculs COBIT 2019</h1>
    <div id="results"></div>

    <script>
        // Matrices de test basées sur l'exemple
        const testMatrix = [1, 4, 0, 0]; // EDM01
        const testBaseline = [1, 3.5, 3, 3];
        const testInputs = [1, 5, 5, 5];
        
        // Fonctions de calcul COBIT 2019
        function calculateCOBITScore(mappingRow, inputValues) {
            if (!mappingRow || !inputValues || mappingRow.length === 0 || inputValues.length === 0) {
                return 0;
            }
            
            let score = 0;
            const minLength = Math.min(mappingRow.length, inputValues.length);
            
            for (let i = 0; i < minLength; i++) {
                const weight = mappingRow[i] || 0;
                const value = inputValues[i] || 0;
                score += weight * value;
            }
            
            return Math.max(0, Math.min(100, score)); // Pas de limite à 5 pour les scores élevés
        }

        function calculateBaselineScore(mappingRow, baselineValues) {
            if (!mappingRow || !baselineValues || mappingRow.length === 0 || baselineValues.length === 0) {
                return 2.5;
            }
            
            let baselineScore = 0;
            const minLength = Math.min(mappingRow.length, baselineValues.length);
            
            for (let i = 0; i < minLength; i++) {
                const weight = mappingRow[i] || 0;
                const baselineValue = baselineValues[i] || 2.5;
                baselineScore += weight * baselineValue;
            }
            
            return Math.max(0, Math.min(100, baselineScore));
        }

        function calculateRelativeImportance(score, baselineScore) {
            try {
                if (baselineScore === 0 || baselineScore === null || baselineScore === undefined) {
                    return 0;
                }
                
                // Formule simplifiée pour correspondre à l'exemple COBIT:
                // RI = ARRONDI.AU.MULTIPLE((score - baseline), 5)
                const simplifiedResult = Math.round((score - baselineScore) / 5) * 5;
                
                return isNaN(simplifiedResult) ? 0 : simplifiedResult;
                
            } catch (error) {
                return 0;
            }
        }

        // Tests
        function runTests() {
            const results = document.getElementById('results');
            
            // Test 1: Calcul du score
            const score = calculateCOBITScore(testMatrix, testInputs);
            const expectedScore = 21;
            const scoreTest = score === expectedScore;
            
            results.innerHTML += `
                <div class="test-result ${scoreTest ? 'success' : 'error'}">
                    <strong>Test Score:</strong><br>
                    Matrice: [${testMatrix.join(', ')}]<br>
                    Inputs: [${testInputs.join(', ')}]<br>
                    Calcul: ${testMatrix[0]}×${testInputs[0]} + ${testMatrix[1]}×${testInputs[1]} + ${testMatrix[2]}×${testInputs[2]} + ${testMatrix[3]}×${testInputs[3]} = ${testMatrix[0]*testInputs[0]} + ${testMatrix[1]*testInputs[1]} + ${testMatrix[2]*testInputs[2]} + ${testMatrix[3]*testInputs[3]} = ${score}<br>
                    Résultat: ${score} (attendu: ${expectedScore}) ${scoreTest ? '✅' : '❌'}
                </div>
            `;
            
            // Test 2: Calcul du baseline
            const baseline = calculateBaselineScore(testMatrix, testBaseline);
            const expectedBaseline = 15;
            const baselineTest = baseline === expectedBaseline;
            
            results.innerHTML += `
                <div class="test-result ${baselineTest ? 'success' : 'error'}">
                    <strong>Test Baseline:</strong><br>
                    Matrice: [${testMatrix.join(', ')}]<br>
                    Baseline: [${testBaseline.join(', ')}]<br>
                    Calcul: ${testMatrix[0]}×${testBaseline[0]} + ${testMatrix[1]}×${testBaseline[1]} + ${testMatrix[2]}×${testBaseline[2]} + ${testMatrix[3]}×${testBaseline[3]} = ${testMatrix[0]*testBaseline[0]} + ${testMatrix[1]*testBaseline[1]} + ${testMatrix[2]*testBaseline[2]} + ${testMatrix[3]*testBaseline[3]} = ${baseline}<br>
                    Résultat: ${baseline} (attendu: ${expectedBaseline}) ${baselineTest ? '✅' : '❌'}
                </div>
            `;
            
            // Test 3: Calcul de la Relative Importance
            const ri = calculateRelativeImportance(score, baseline);
            const expectedRI = 5;
            const riTest = ri === expectedRI;
            
            results.innerHTML += `
                <div class="test-result ${riTest ? 'success' : 'error'}">
                    <strong>Test Relative Importance:</strong><br>
                    Score: ${score}, Baseline: ${baseline}<br>
                    Calcul: ARRONDI.AU.MULTIPLE((${score} - ${baseline}), 5) = ARRONDI.AU.MULTIPLE(${score - baseline}, 5) = ${ri}<br>
                    Résultat: ${ri} (attendu: ${expectedRI}) ${riTest ? '✅' : '❌'}
                </div>
            `;
            
            // Résumé
            const allTestsPassed = scoreTest && baselineTest && riTest;
            results.innerHTML += `
                <div class="test-result ${allTestsPassed ? 'success' : 'error'}">
                    <strong>Résumé des Tests:</strong><br>
                    ${allTestsPassed ? '✅ Tous les tests passent ! Les calculs COBIT 2019 sont corrects.' : '❌ Certains tests échouent. Ajustements nécessaires.'}
                </div>
            `;
            
            if (!allTestsPassed) {
                results.innerHTML += `
                    <div class="test-result info">
                        <strong>Suggestions d'ajustement:</strong><br>
                        ${!scoreTest ? `- Ajuster la matrice EDM01 pour obtenir Score=${expectedScore}<br>` : ''}
                        ${!baselineTest ? `- Ajuster les valeurs baseline pour obtenir Baseline=${expectedBaseline}<br>` : ''}
                        ${!riTest ? `- Ajuster la formule RI pour obtenir RI=${expectedRI}<br>` : ''}
                    </div>
                `;
            }
        }

        // Lancer les tests au chargement
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
