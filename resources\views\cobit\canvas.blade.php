<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Final - Résultats COBIT 2019</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .kpmg-blue { color: #00338D; }
        .kpmg-bg { background-color: #00338D; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-5px); box-shadow: 0 15px 30px rgba(0,0,0,0.1); }
        .animate-pulse-slow { animation: pulse 3s infinite; }
        .results-canvas { 
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="kpmg-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-white hover:text-blue-200 transition-colors">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <div class="bg-white p-2 rounded">
                        <svg width="40" height="20" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                            <text x="10" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#00338D">KPMG</text>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">Canvas de Résultats Finaux</h1>
                        <p class="text-blue-200 text-sm">Analyse Complète COBIT 2019</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm animate-pulse-slow">✅ Évaluation Complète</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Canvas Principal -->
    <div class="container mx-auto px-6 py-8">
        <div class="results-canvas mb-8">
            <div class="text-center mb-8">
                <h2 class="text-4xl font-bold kpmg-blue mb-4">
                    <i class="fas fa-trophy mr-3 text-yellow-500"></i>
                    Résultats Finaux COBIT 2019
                </h2>
                @if(isset($evaluation))
                <div class="bg-blue-50 rounded-lg p-4 mb-4 inline-block">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">{{ $evaluation->nom_entreprise }}</h3>
                    <div class="flex items-center justify-center space-x-4 text-sm text-blue-600">
                        <span><i class="fas fa-building mr-1"></i>{{ $evaluation->taille_entreprise }}</span>
                        <span><i class="fas fa-user mr-1"></i>{{ $evaluation->user_name ?? 'Utilisateur' }}</span>
                        <span><i class="fas fa-calendar mr-1"></i>{{ $evaluation->updated_at->format('d/m/Y H:i') }}</span>
                    </div>
                    @if($evaluation->contraintes)
                    <div class="mt-2 text-sm text-blue-600">
                        <i class="fas fa-info-circle mr-1"></i>{{ $evaluation->contraintes }}
                    </div>
                    @endif
                    <div class="mt-2 text-green-600 text-sm">
                        <i class="fas fa-check-circle mr-1"></i>Évaluation terminée - Score: {{ $scoreGlobal }}/5
                    </div>
                </div>
                @endif
                <p class="text-xl text-gray-600">Analyse complète de votre évaluation des 10 Design Factors</p>
            </div>

            <!-- Métriques Globales -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="text-center p-6 bg-white rounded-xl shadow-lg card-hover">
                    <div class="text-4xl font-bold text-green-600 mb-2" id="maturity-level">0</div>
                    <div class="text-sm text-gray-600">Niveau de Maturité</div>
                    <div class="mt-2 text-xs text-gray-500" id="maturity-description">-</div>
                </div>

                <div class="text-center p-6 bg-white rounded-xl shadow-lg card-hover">
                    <div class="text-4xl font-bold text-purple-600 mb-2" id="total-objectives">0</div>
                    <div class="text-sm text-gray-600">Objectifs Impactés</div>
                    <div class="mt-2 text-xs text-gray-500">sur 40 objectifs COBIT</div>
                </div>

                <div class="text-center p-6 bg-white rounded-xl shadow-lg card-hover">
                    <div class="text-4xl font-bold text-orange-600 mb-2" id="completion-rate">100%</div>
                    <div class="text-sm text-gray-600">Taux de Complétude</div>
                    <div class="mt-2 text-xs text-green-600">✓ Évaluation terminée</div>
                </div>
            </div>

            <!-- Graphiques Principaux -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Graphique Radar Global -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                    <h3 class="text-xl font-bold kmpg-blue mb-4 flex items-center">
                        <i class="fas fa-chart-area mr-2"></i>
                        Vue d'ensemble - Radar Chart
                    </h3>
                    <div class="relative h-80">
                        <canvas id="final-radar-chart"></canvas>
                    </div>
                </div>
                
                <!-- Graphique de Performance par DF -->
                <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                    <h3 class="text-xl font-bold kmpg-blue mb-4 flex items-center">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Performance par Design Factor
                    </h3>
                    <div class="relative h-80">
                        <canvas id="df-performance-chart"></canvas>
                    </div>
                </div>
            </div>



            <!-- Meilleurs Objectifs COBIT -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8 card-hover">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold kmpg-blue flex items-center">
                        <i class="fas fa-star mr-2 text-yellow-500"></i>
                        Meilleurs Objectifs COBIT ({{ count($bestObjectives) }} objectifs)
                    </h3>
                    <button onclick="generateRoadmap()" class="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:from-purple-700 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg">
                        <i class="fas fa-route mr-2"></i>Générer Roadmap IA
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($bestObjectives as $index => $objective)
                    <div class="bg-gradient-to-br from-green-50 to-blue-50 rounded-lg p-4 border-l-4 border-green-500 hover:shadow-md transition-all">
                        <div class="flex items-center justify-between mb-2">
                            <span class="font-bold text-lg text-green-700">{{ $objective['objective'] ?? 'N/A' }}</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-semibold">
                                {{ number_format($objective['score'] ?? 0, 1) }}
                            </span>
                        </div>
                        <div class="text-sm text-gray-600 mb-2">
                            <strong>Priorité:</strong>
                            <span class="px-2 py-1 rounded text-xs font-semibold
                                {{ ($objective['priority'] ?? 'L') == 'H' ? 'bg-red-100 text-red-800' :
                                   (($objective['priority'] ?? 'L') == 'M' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') }}">
                                {{ $objective['priority'] ?? 'L' }}
                            </span>
                        </div>
                        <div class="text-xs text-gray-500">
                            Gap: {{ number_format($objective['gap'] ?? 0, 2) }} |
                            Baseline: {{ number_format($objective['baseline'] ?? 0, 1) }}
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full transition-all"
                                     style="width: {{ min(100, max(0, ($objective['score'] ?? 0) / 5 * 100)) }}%"></div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Roadmap IA (Section cachée par défaut) -->
            <div id="roadmap-section" class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl shadow-lg p-6 mb-8 card-hover" style="display: none;">
                <h3 class="text-xl font-bold text-purple-700 mb-6 flex items-center">
                    <i class="fas fa-map-marked-alt mr-2"></i>
                    Roadmap de Mise en Œuvre IA
                </h3>
                <div id="roadmap-content">
                    <!-- Contenu généré dynamiquement -->
                </div>
            </div>

            <!-- Recommandations IA Globales -->
            <div class="bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl shadow-lg p-6 card-hover">
                <h3 class="text-xl font-bold text-purple-800 mb-6 flex items-center">
                    <i class="fas fa-brain mr-2"></i>
                    Recommandations IA Globales
                </h3>
                <div id="global-recommendations" class="space-y-4">
                    <!-- Recommandations générées dynamiquement -->
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="text-center space-x-4">
            <button onclick="exportPDF()" class="bg-red-600 text-white px-8 py-3 rounded-lg font-bold hover:bg-red-700 transition-colors">
                <i class="fas fa-file-pdf mr-2"></i>Exporter PDF
            </button>
            <button onclick="exportExcel()" class="bg-green-600 text-white px-8 py-3 rounded-lg font-bold hover:bg-green-700 transition-colors">
                <i class="fas fa-file-excel mr-2"></i>Exporter Excel
            </button>
            <button onclick="shareResults()" class="kmpg-bg text-white px-8 py-3 rounded-lg font-bold hover:bg-blue-700 transition-colors">
                <i class="fas fa-share mr-2"></i>Partager
            </button>
            <button onclick="newEvaluation()" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-bold hover:bg-gray-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Nouvelle Évaluation
            </button>
        </div>
    </div>

    <script>
        // Variables globales
        let finalData = {
            designFactors: [
                @foreach($designFactors as $df)
                @php $dfNumber = str_replace('DF', '', $df->code); @endphp
                {
                    code: '{{ $df->code }}',
                    title: '{{ $df->title }}',
                    number: {{ $dfNumber }},
                    score: Math.random() * 3 + 2, // Simulation
                    completed: true,
                    objectives: Math.floor(Math.random() * 10) + 5
                },
                @endforeach
            ]
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            calculateGlobalMetrics();
            createCharts();
            generateRecommendations();
        });

        // Calculer les métriques globales
        function calculateGlobalMetrics() {
            // Calculer le niveau de maturité basé sur les DF complétés
            const globalScore = {{ $scoreGlobal ?? 3 }};
            const maturityLevel = Math.round(globalScore);
            const totalObjectives = {{ count($bestObjectives ?? []) }};

            // Afficher seulement le niveau de maturité et les objectifs
            document.getElementById('maturity-level').textContent = maturityLevel;
            document.getElementById('total-objectives').textContent = totalObjectives;

            // Description du niveau de maturité
            const maturityDescriptions = {
                1: 'Initial',
                2: 'Géré',
                3: 'Défini',
                4: 'Quantitativement géré',
                5: 'Optimisé'
            };
            document.getElementById('maturity-description').textContent = maturityDescriptions[maturityLevel] || 'Non défini';
        }

        // Créer les graphiques
        function createCharts() {
            // Graphique Radar Final
            const radarCtx = document.getElementById('final-radar-chart');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: finalData.designFactors.map(df => df.code),
                    datasets: [{
                        label: 'Scores Finaux',
                        data: finalData.designFactors.map(df => df.score),
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgb(59, 130, 246)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(59, 130, 246)'
                    }, {
                        label: 'Baseline (2.5)',
                        data: new Array(10).fill(2.5),
                        backgroundColor: 'rgba(156, 163, 175, 0.1)',
                        borderColor: 'rgb(156, 163, 175)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgb(156, 163, 175)',
                        pointBorderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 5,
                            ticks: { stepSize: 1 }
                        }
                    },
                    plugins: {
                        legend: { position: 'bottom' },
                        title: {
                            display: true,
                            text: 'Performance Globale des Design Factors'
                        }
                    }
                }
            });

            // Graphique de Performance par DF
            const barCtx = document.getElementById('df-performance-chart');
            new Chart(barCtx, {
                type: 'bar',
                data: {
                    labels: finalData.designFactors.map(df => df.code),
                    datasets: [{
                        label: 'Score',
                        data: finalData.designFactors.map(df => df.score),
                        backgroundColor: finalData.designFactors.map(df => 
                            df.score >= 4 ? 'rgba(16, 185, 129, 0.8)' :
                            df.score >= 3 ? 'rgba(59, 130, 246, 0.8)' :
                            df.score >= 2 ? 'rgba(245, 158, 11, 0.8)' :
                            'rgba(239, 68, 68, 0.8)'
                        ),
                        borderColor: finalData.designFactors.map(df => 
                            df.score >= 4 ? 'rgb(16, 185, 129)' :
                            df.score >= 3 ? 'rgb(59, 130, 246)' :
                            df.score >= 2 ? 'rgb(245, 158, 11)' :
                            'rgb(239, 68, 68)'
                        ),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 5,
                            ticks: { stepSize: 1 }
                        }
                    },
                    plugins: {
                        legend: { display: false },
                        title: {
                            display: true,
                            text: 'Scores par Design Factor'
                        }
                    }
                }
            });
        }

        // Navigation
        function goHome() {
            window.location.href = '/cobit/home';
        }

        // Actions
        function exportPDF() {
            // Créer un formulaire pour l'export PDF
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/cobit/export/pdf';

            // Ajouter le token CSRF
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function exportExcel() {
            // Créer un formulaire pour l'export Excel
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/cobit/export/excel';

            // Ajouter le token CSRF
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        function shareResults() {
            alert('Fonctionnalité de partage en cours de développement...');
        }

        function newEvaluation() {
            if (confirm('Commencer une nouvelle évaluation ? (Les données actuelles seront perdues)')) {
                window.location.href = '/cobit/home';
            }
        }



        // Générer les recommandations
        function generateRecommendations() {
            const recommendationsEl = document.getElementById('global-recommendations');
            const recommendations = [];

            const globalScore = finalData.designFactors.reduce((sum, df) => sum + df.score, 0) / finalData.designFactors.length;
            const lowScoreDFs = finalData.designFactors.filter(df => df.score < 2.5);
            const highScoreDFs = finalData.designFactors.filter(df => df.score >= 4);

            // Analyse globale
            if (globalScore >= 4) {
                recommendations.push({
                    icon: '🏆',
                    type: 'success',
                    title: 'Performance Exceptionnelle',
                    text: 'Votre organisation démontre une excellente maturité en gouvernance IT. Maintenez ces standards élevés et partagez les bonnes pratiques.'
                });
            } else if (globalScore >= 3) {
                recommendations.push({
                    icon: '📈',
                    type: 'improvement',
                    title: 'Bonne Performance Globale',
                    text: 'Votre gouvernance IT est bien établie. Concentrez-vous sur l\'optimisation continue et l\'innovation.'
                });
            } else if (globalScore >= 2) {
                recommendations.push({
                    icon: '⚠️',
                    type: 'warning',
                    title: 'Performance Modérée',
                    text: 'Des améliorations sont nécessaires dans plusieurs domaines. Priorisez les Design Factors critiques.'
                });
            } else {
                recommendations.push({
                    icon: '🚨',
                    type: 'critical',
                    title: 'Action Urgente Requise',
                    text: 'Votre gouvernance IT nécessite une refonte majeure. Élaborez un plan d\'amélioration immédiat.'
                });
            }

            // Recommandations spécifiques
            if (lowScoreDFs.length > 0) {
                recommendations.push({
                    icon: '🎯',
                    type: 'action',
                    title: 'Design Factors Prioritaires',
                    text: `Concentrez vos efforts sur : ${lowScoreDFs.map(df => df.code).join(', ')}. Ces domaines nécessitent une attention immédiate.`
                });
            }

            if (highScoreDFs.length > 0) {
                recommendations.push({
                    icon: '✅',
                    type: 'success',
                    title: 'Points Forts Identifiés',
                    text: `Excellente performance sur : ${highScoreDFs.map(df => df.code).join(', ')}. Utilisez ces succès comme modèles pour les autres domaines.`
                });
            }

            // Recommandations stratégiques
            recommendations.push({
                icon: '📋',
                type: 'strategy',
                title: 'Plan d\'Action Recommandé',
                text: '1. Établir un comité de gouvernance IT, 2. Définir des KPIs de suivi, 3. Planifier des revues trimestrielles, 4. Former les équipes sur COBIT 2019.'
            });

            // Afficher les recommandations
            recommendationsEl.innerHTML = '';
            recommendations.forEach(rec => {
                const div = document.createElement('div');
                div.className = `p-4 rounded-lg border-l-4 ${getRecommendationClass(rec.type)}`;
                div.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <span class="text-2xl">${rec.icon}</span>
                        <div class="flex-1">
                            <h4 class="font-bold text-lg mb-2">${rec.title}</h4>
                            <p class="text-sm leading-relaxed">${rec.text}</p>
                        </div>
                    </div>
                `;
                recommendationsEl.appendChild(div);
            });
        }

        function getRecommendationClass(type) {
            switch(type) {
                case 'critical': return 'border-red-500 bg-red-50';
                case 'warning': return 'border-yellow-500 bg-yellow-50';
                case 'improvement': return 'border-blue-500 bg-blue-50';
                case 'success': return 'border-green-500 bg-green-50';
                case 'action': return 'border-purple-500 bg-purple-50';
                case 'strategy': return 'border-indigo-500 bg-indigo-50';
                default: return 'border-gray-500 bg-gray-50';
            }
        }

        // Générer le roadmap IA
        function generateRoadmap() {
            const roadmapSection = document.getElementById('roadmap-section');
            const roadmapContent = document.getElementById('roadmap-content');

            // Afficher la section
            roadmapSection.style.display = 'block';
            roadmapSection.scrollIntoView({ behavior: 'smooth' });

            // Simuler un chargement
            roadmapContent.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-4xl text-purple-600 mb-4"></i>
                    <p class="text-lg text-gray-600">Génération du roadmap en cours...</p>
                </div>
            `;

            // Générer le roadmap après 2 secondes
            setTimeout(() => {
                const bestObjectives = @json($bestObjectives ?? []);
                const scoreGlobal = {{ $scoreGlobal ?? 3 }};

                let roadmapHTML = `
                    <div class="mb-6">
                        <h4 class="text-lg font-bold text-purple-700 mb-3">🎯 Plan de Mise en Œuvre Recommandé</h4>
                        <div class="bg-white rounded-lg p-4 border border-purple-200">
                            <p class="text-gray-700">Basé sur votre score global de <strong>${scoreGlobal.toFixed(1)}/5</strong> et vos ${bestObjectives.length} objectifs prioritaires.</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Phase 1: Court terme (0-6 mois) -->
                        <div class="bg-white rounded-lg p-6 border-l-4 border-green-500">
                            <h5 class="font-bold text-green-700 mb-3 flex items-center">
                                <i class="fas fa-rocket mr-2"></i>Phase 1: Court terme
                            </h5>
                            <p class="text-sm text-gray-600 mb-3">0-6 mois</p>
                            <ul class="space-y-2 text-sm">
                `;

                // Ajouter les 3 premiers objectifs pour la phase 1
                bestObjectives.slice(0, 3).forEach((obj, index) => {
                    roadmapHTML += `
                        <li class="flex items-start">
                            <span class="bg-green-100 text-green-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">${index + 1}</span>
                            <div>
                                <strong>${obj.objective || 'N/A'}</strong>
                                <div class="text-xs text-gray-500">Priorité: ${obj.priority || 'M'} | Score: ${(obj.score || 0).toFixed(1)}</div>
                            </div>
                        </li>
                    `;
                });

                roadmapHTML += `
                            </ul>
                        </div>

                        <!-- Phase 2: Moyen terme (6-12 mois) -->
                        <div class="bg-white rounded-lg p-6 border-l-4 border-yellow-500">
                            <h5 class="font-bold text-yellow-700 mb-3 flex items-center">
                                <i class="fas fa-cogs mr-2"></i>Phase 2: Moyen terme
                            </h5>
                            <p class="text-sm text-gray-600 mb-3">6-12 mois</p>
                            <ul class="space-y-2 text-sm">
                `;

                // Ajouter les objectifs suivants pour la phase 2
                bestObjectives.slice(3, 6).forEach((obj, index) => {
                    roadmapHTML += `
                        <li class="flex items-start">
                            <span class="bg-yellow-100 text-yellow-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">${index + 4}</span>
                            <div>
                                <strong>${obj.objective || 'N/A'}</strong>
                                <div class="text-xs text-gray-500">Priorité: ${obj.priority || 'M'} | Score: ${(obj.score || 0).toFixed(1)}</div>
                            </div>
                        </li>
                    `;
                });

                roadmapHTML += `
                            </ul>
                        </div>

                        <!-- Phase 3: Long terme (12+ mois) -->
                        <div class="bg-white rounded-lg p-6 border-l-4 border-blue-500">
                            <h5 class="font-bold text-blue-700 mb-3 flex items-center">
                                <i class="fas fa-chart-line mr-2"></i>Phase 3: Long terme
                            </h5>
                            <p class="text-sm text-gray-600 mb-3">12+ mois</p>
                            <ul class="space-y-2 text-sm">
                `;

                // Ajouter les objectifs restants pour la phase 3
                bestObjectives.slice(6).forEach((obj, index) => {
                    roadmapHTML += `
                        <li class="flex items-start">
                            <span class="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">${index + 7}</span>
                            <div>
                                <strong>${obj.objective || 'N/A'}</strong>
                                <div class="text-xs text-gray-500">Priorité: ${obj.priority || 'M'} | Score: ${(obj.score || 0).toFixed(1)}</div>
                            </div>
                        </li>
                    `;
                });

                roadmapHTML += `
                            </ul>
                        </div>
                    </div>

                    <div class="mt-6 bg-gradient-to-r from-purple-100 to-blue-100 rounded-lg p-4">
                        <h5 class="font-bold text-purple-700 mb-2 flex items-center">
                            <i class="fas fa-lightbulb mr-2"></i>Recommandations IA
                        </h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• Commencez par les objectifs à haute priorité pour un impact rapide</li>
                            <li>• Allouez 60% des ressources à la Phase 1, 30% à la Phase 2, 10% à la Phase 3</li>
                            <li>• Réévaluez le roadmap tous les 3 mois pour ajuster les priorités</li>
                            <li>• Impliquez les parties prenantes clés dès la Phase 1</li>
                        </ul>
                    </div>
                `;

                roadmapContent.innerHTML = roadmapHTML;
            }, 2000);
        }

        // Navigation
        function goHome() {
            window.location.href = '/cobit/home';
        }
    </script>
</body>
</html>
