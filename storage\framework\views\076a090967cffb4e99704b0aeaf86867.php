

<!-- Styles CSS pour le chatbot -->
<link rel="stylesheet" href="<?php echo e(asset('css/chatbot.css')); ?>">

<!-- Meta CSRF pour les requêtes AJAX -->
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<!-- Le widget sera créé dynamiquement par JavaScript -->
<!-- Aucun HTML statique nécessaire ici -->

<!-- JavaScript du chatbot -->
<script src="<?php echo e(asset('js/chatbot.js')); ?>"></script>

<script>
    // Configuration spécifique pour cette instance
    document.addEventListener('DOMContentLoaded', function() {
        // Vérifier que le chatbot est bien initialisé
        if (window.cobitChatbot) {
            console.log('✅ Chatbot COBIT 2019 prêt sur la page <?php echo e(request()->path()); ?>');
            
            // Optionnel : personnalisation selon la page
            <?php if(request()->is('cobit/home')): ?>
                // Configuration spéciale pour la page d'accueil
                console.log('🏠 Chatbot configuré pour la page d\'accueil');
            <?php endif; ?>
        } else {
            console.warn('⚠️ Chatbot COBIT non initialisé');
        }
    });
</script>

<style>
    /* Styles spécifiques pour l'intégration dans le thème KPMG */
    .chatbot-container {
        /* S'assurer que le chatbot ne gêne pas les autres éléments */
        z-index: 9999;
    }
    
    /* Adaptation pour les écrans mobiles */
    @media (max-width: 768px) {
        .chatbot-container {
            bottom: 15px;
            right: 15px;
        }
    }
    
    /* Intégration harmonieuse avec le design KPMG */
    .chatbot-toggle {
        background: linear-gradient(135deg, #00338D 0%, #0066CC 100%);
        box-shadow: 0 8px 32px rgba(0, 51, 141, 0.3);
    }
    
    .chatbot-toggle:hover {
        box-shadow: 0 12px 40px rgba(0, 51, 141, 0.4);
    }
    
    /* Animation d'entrée douce */
    .chatbot-container {
        animation: chatbotFadeIn 0.5s ease-out;
    }
    
    @keyframes chatbotFadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
<?php /**PATH C:\Users\<USER>\Desktop\symfcopite\symf\symfcobite\cobit-laravel\resources\views\components\chatbot.blade.php ENDPATH**/ ?>