# Workflow d'Évaluation COBIT 2019 - Système Complet

## Vue d'ensemble

Ce système implémente un workflow complet d'évaluation COBIT 2019 avec sauvegarde automatique des canvas dans l'historique. Chaque évaluation est personnalisée avec les informations de l'entreprise et génère un canvas unique.

## Cycle Complet d'Évaluation

### 1. Démarrage d'une Nouvelle Évaluation

**Action :** Cliquer sur "Commencer l'Évaluation" sur la page d'accueil

**Processus :**
- Un modal s'ouvre demandant :
  - Nom de l'entreprise (obligatoire)
  - Taille de l'entreprise (obligatoire)
- Les informations sont sauvegardées en session
- Redirection automatique vers le premier Design Factor (DF1)

### 2. Évaluation des 10 Design Factors

**Processus :**
- L'utilisateur évalue chaque DF de 1 à 10
- Les données sont sauvegardées automatiquement en session
- Progression trackée en temps réel
- Possibilité de naviguer entre les DF

### 3. Génération du Canvas Final

**Déclenchement :** Après completion des 10 DF

**Processus automatique :**
- Calcul des résultats finaux basés sur les matrices COBIT
- Génération du canvas avec :
  - Informations de l'entreprise
  - Nom et rôle de l'utilisateur
  - Date et heure de l'évaluation
  - Scores calculés par domaine
  - Recommandations

### 4. Sauvegarde Automatique dans l'Historique

**Déclenchement :** Dès l'affichage du canvas final (si 10 DF complétés)

**Données sauvegardées :**
- Informations entreprise (nom, taille)
- Informations utilisateur (nom, rôle)
- Données complètes des 10 DF
- Résultats calculés du canvas
- Moyennes par domaine COBIT
- Score global
- Timestamps (début et fin d'évaluation)

### 5. Consultation de l'Historique

**Fonctionnalités :**
- Liste de toutes les évaluations terminées
- Affichage des informations clés (entreprise, utilisateur, score, date)
- Lien direct vers chaque canvas sauvegardé
- Possibilité de démarrer une nouvelle évaluation

## Structure Technique

### Base de Données

**Table : `canvas_historiques`**
```sql
- id (primary key)
- company_name (nom entreprise)
- company_size (taille entreprise)
- user_name (nom utilisateur)
- user_role (rôle utilisateur)
- evaluation_data (JSON - données des 10 DF)
- canvas_results (JSON - résultats calculés)
- domain_averages (JSON - moyennes par domaine)
- score_global (score global calculé)
- completed_dfs (nombre de DF complétés)
- status (En cours/Terminée)
- evaluation_started_at (début évaluation)
- evaluation_completed_at (fin évaluation)
- created_at, updated_at (timestamps Laravel)
```

### Contrôleur Principal

**CobitController - Méthodes clés :**
- `nouvelleEvaluation()` : Initialise une nouvelle évaluation
- `canvasFinal()` : Génère et affiche le canvas final
- `saveCanvasToHistory()` : Sauvegarde automatique en base
- `historique()` : Affiche la liste des évaluations
- `viewCanvasFromHistory()` : Affiche un canvas spécifique

### Vues Principales

1. **home.blade.php** : Page d'accueil avec modal de nouvelle évaluation
2. **df-detail.blade.php** : Évaluation individuelle des Design Factors
3. **canvas-final.blade.php** : Affichage du canvas généré
4. **historique.blade.php** : Liste des évaluations sauvegardées
5. **canvas-historique.blade.php** : Affichage d'un canvas depuis l'historique

## Workflow Utilisateur

```
[Page d'accueil] 
    ↓ Clic "Commencer l'Évaluation"
[Modal informations entreprise]
    ↓ Validation formulaire
[DF1] → [DF2] → ... → [DF10]
    ↓ Tous les DF complétés
[Canvas Final] 
    ↓ Sauvegarde automatique
[Historique] ← Consultation possible
    ↓ Clic sur un canvas
[Canvas Historique]
```

## Fonctionnalités Avancées

### Personnalisation par Évaluation
- Chaque évaluation est unique et identifiée
- Informations contextuelles (entreprise, utilisateur)
- Horodatage précis de l'évaluation

### Sauvegarde Intelligente
- Sauvegarde automatique uniquement si 10 DF complétés
- Évite les doublons grâce au flag de session
- Calcul automatique du score global

### Historique Complet
- Consultation illimitée des évaluations passées
- Données figées au moment de la sauvegarde
- Interface intuitive avec filtres visuels

### Continuité du Workflow
- Possibilité de démarrer une nouvelle évaluation depuis n'importe où
- Navigation fluide entre les sections
- État de session préservé pendant l'évaluation

## Avantages du Système

1. **Traçabilité Complète** : Chaque évaluation est documentée et horodatée
2. **Personnalisation** : Adaptation aux spécificités de chaque entreprise
3. **Historique Persistant** : Conservation des résultats pour analyse comparative
4. **Workflow Intuitif** : Processus guidé et automatisé
5. **Données Structurées** : Format JSON pour analyses futures
6. **Scalabilité** : Architecture extensible pour nouvelles fonctionnalités

## Utilisation

1. **Première évaluation :** Suivre le workflow complet
2. **Évaluations suivantes :** Répéter le processus pour d'autres entreprises/contextes
3. **Consultation :** Accéder à l'historique pour revoir les résultats passés
4. **Analyse :** Comparer les scores entre différentes évaluations
