<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use App\Services\OllamaService;

/**
 * Commande pour optimiser automatiquement le chatbot
 */
class OptimizeChatbot extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'chatbot:optimize {--clear-cache : Nettoyer le cache} {--test-performance : Tester les performances}';

    /**
     * The console command description.
     */
    protected $description = 'Optimise automatiquement les performances du chatbot COBIT';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Optimisation du chatbot COBIT en cours...');

        // Nettoyer le cache si demandé
        if ($this->option('clear-cache')) {
            $this->clearChatbotCache();
        }

        // Optimiser la configuration
        $this->optimizeConfiguration();

        // Tester les performances si demandé
        if ($this->option('test-performance')) {
            $this->testPerformance();
        }

        // Vérifier les services
        $this->checkServices();

        $this->info('✅ Optimisation terminée !');
        $this->displayRecommendations();
    }

    /**
     * Nettoyer le cache du chatbot
     */
    private function clearChatbotCache(): void
    {
        $this->info('🧹 Nettoyage du cache...');

        // Patterns de cache à nettoyer
        $patterns = [
            'chatbot_response_*',
            'chatbot_health_status',
            'ollama_response_*',
            'ollama_connection_status',
            'chatbot_metrics_*',
            'chatbot_active_requests'
        ];

        $cleared = 0;
        foreach ($patterns as $pattern) {
            try {
                // Pour Redis
                if (config('cache.default') === 'redis') {
                    $keys = Cache::getStore()->getRedis()->keys($pattern);
                    foreach ($keys as $key) {
                        Cache::forget($key);
                        $cleared++;
                    }
                } else {
                    // Pour les autres drivers, nettoyer tout
                    Cache::flush();
                    $cleared = 1;
                    break;
                }
            } catch (\Exception $e) {
                $this->warn("Erreur lors du nettoyage: {$e->getMessage()}");
            }
        }

        $this->info("✅ Cache nettoyé: {$cleared} entrées supprimées");
    }

    /**
     * Optimiser la configuration
     */
    private function optimizeConfiguration(): void
    {
        $this->info('⚙️ Optimisation de la configuration...');

        // Vérifier la configuration du cache
        $cacheDriver = config('cache.default');
        if ($cacheDriver !== 'redis') {
            $this->warn('⚠️ Recommandation: Utilisez Redis pour de meilleures performances');
            $this->line('   Ajoutez CACHE_DRIVER=redis dans votre .env');
        } else {
            $this->info('✅ Cache Redis configuré');
        }

        // Vérifier la configuration des queues
        $queueDriver = config('queue.default');
        if ($queueDriver === 'sync') {
            $this->warn('⚠️ Recommandation: Utilisez Redis pour les queues asynchrones');
            $this->line('   Ajoutez QUEUE_CONNECTION=redis dans votre .env');
        } else {
            $this->info('✅ Queues asynchrones configurées');
        }

        // Optimiser les caches Laravel
        try {
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');
            $this->info('✅ Caches Laravel optimisés');
        } catch (\Exception $e) {
            $this->warn("Erreur lors de l'optimisation des caches: {$e->getMessage()}");
        }
    }

    /**
     * Tester les performances
     */
    private function testPerformance(): void
    {
        $this->info('🔍 Test des performances...');

        // Test de connexion Ollama
        $startTime = microtime(true);
        $ollamaService = new OllamaService();
        $ollamaConnected = $ollamaService->testConnection();
        $ollamaDuration = microtime(true) - $startTime;

        if ($ollamaConnected) {
            $this->info("✅ Ollama connecté en {$ollamaDuration:.2f}s");
            if ($ollamaDuration > 3) {
                $this->warn('⚠️ Connexion Ollama lente (> 3s)');
            }
        } else {
            $this->error('❌ Ollama non accessible');
        }

        // Test du cache
        $startTime = microtime(true);
        Cache::put('test_performance', 'test', 1);
        $value = Cache::get('test_performance');
        Cache::forget('test_performance');
        $cacheDuration = microtime(true) - $startTime;

        if ($value === 'test') {
            $this->info("✅ Cache fonctionnel en {$cacheDuration:.3f}s");
            if ($cacheDuration > 0.1) {
                $this->warn('⚠️ Cache lent (> 0.1s)');
            }
        } else {
            $this->error('❌ Cache non fonctionnel');
        }
    }

    /**
     * Vérifier les services
     */
    private function checkServices(): void
    {
        $this->info('🔍 Vérification des services...');

        // Vérifier Redis
        try {
            if (config('cache.default') === 'redis') {
                Cache::put('redis_test', 'ok', 1);
                $redisOk = Cache::get('redis_test') === 'ok';
                Cache::forget('redis_test');
                
                if ($redisOk) {
                    $this->info('✅ Redis fonctionnel');
                } else {
                    $this->error('❌ Redis non fonctionnel');
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Erreur Redis: {$e->getMessage()}");
        }

        // Vérifier Ollama
        $ollamaService = new OllamaService();
        if ($ollamaService->testConnection()) {
            $this->info('✅ Ollama accessible');
        } else {
            $this->error('❌ Ollama non accessible');
            $this->line('   Vérifiez que Ollama est démarré: ollama serve');
        }
    }

    /**
     * Afficher les recommandations
     */
    private function displayRecommendations(): void
    {
        $this->info('📋 Recommandations pour optimiser davantage:');
        
        $recommendations = [
            '1. Utilisez Redis: CACHE_DRIVER=redis et QUEUE_CONNECTION=redis',
            '2. Activez le mode asynchrone: CHATBOT_ASYNC_PROCESSING=true',
            '3. Réduisez les timeouts: CHATBOT_TIMEOUT=5, OLLAMA_TIMEOUT=5',
            '4. Démarrez un worker de queue: php artisan queue:work',
            '5. Surveillez les performances: /cobit/chatbot/dashboard',
            '6. Utilisez un modèle Ollama plus petit pour plus de vitesse',
            '7. Configurez un reverse proxy (nginx) pour la production'
        ];

        foreach ($recommendations as $recommendation) {
            $this->line("   {$recommendation}");
        }

        $this->newLine();
        $this->info('🎯 Objectif: Réponses en moins de 5 secondes !');
    }
}
