<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use App\Services\ChatbotPerformanceService;
use App\Services\OllamaService;

/**
 * Contrôleur pour le tableau de bord de performance du chatbot
 */
class ChatbotDashboardController extends Controller
{
    /**
     * Afficher le tableau de bord de performance
     */
    public function dashboard(): JsonResponse
    {
        $stats = ChatbotPerformanceService::getStats();
        $recommendations = ChatbotPerformanceService::getOptimizationRecommendations();
        
        // Vérifier le statut des services
        $ollamaService = new OllamaService();
        $ollamaStatus = $ollamaService->testConnection();
        
        // Vérifier le statut du chatbot
        $chatbotController = new ChatbotController();
        $healthResponse = $chatbotController->health();
        $chatbotStatus = $healthResponse->getStatusCode() === 200;
        
        return response()->json([
            'status' => 'success',
            'dashboard' => [
                'services' => [
                    'chatbot' => $chatbotStatus,
                    'ollama' => $ollamaStatus,
                    'cache' => $this->checkCacheStatus(),
                    'queue' => $this->checkQueueStatus(),
                ],
                'performance' => $stats,
                'recommendations' => $recommendations,
                'system_info' => [
                    'active_requests' => cache()->get('chatbot_active_requests', 0),
                    'max_concurrent' => config('chatbot.performance.max_concurrent_requests', 5),
                    'cache_driver' => config('cache.default'),
                    'queue_driver' => config('queue.default'),
                    'async_enabled' => config('chatbot.performance.async_processing', false),
                ],
                'configuration' => [
                    'api_timeout' => config('chatbot.api.timeout', 30),
                    'cache_enabled' => config('chatbot.cache.enabled', true),
                    'ollama_model' => config('chatbot.ollama.model', 'llama3.1:8b'),
                    'ollama_timeout' => config('chatbot.ollama.timeout', 15),
                ]
            ]
        ]);
    }
    
    /**
     * Obtenir les métriques en temps réel
     */
    public function metrics(): JsonResponse
    {
        $stats = ChatbotPerformanceService::getStats();
        
        return response()->json([
            'status' => 'success',
            'metrics' => $stats,
            'timestamp' => now()->toISOString()
        ]);
    }
    
    /**
     * Nettoyer le cache du chatbot
     */
    public function clearCache(): JsonResponse
    {
        try {
            // Nettoyer les caches spécifiques au chatbot
            $patterns = [
                'chatbot_response_*',
                'chatbot_health_status',
                'ollama_response_*',
                'ollama_connection_status'
            ];
            
            $cleared = 0;
            foreach ($patterns as $pattern) {
                // Note: Cette méthode dépend du driver de cache utilisé
                // Pour Redis, on pourrait utiliser des commandes spécifiques
                $keys = cache()->getStore()->getRedis()->keys($pattern);
                foreach ($keys as $key) {
                    cache()->forget($key);
                    $cleared++;
                }
            }
            
            return response()->json([
                'status' => 'success',
                'message' => "Cache nettoyé: {$cleared} entrées supprimées"
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur lors du nettoyage du cache: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Redémarrer les services (si possible)
     */
    public function restartServices(): JsonResponse
    {
        try {
            // Nettoyer les compteurs
            cache()->forget('chatbot_active_requests');
            
            // Nettoyer les caches de statut
            cache()->forget('chatbot_health_status');
            cache()->forget('ollama_connection_status');
            
            return response()->json([
                'status' => 'success',
                'message' => 'Services redémarrés (caches nettoyés)'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Erreur lors du redémarrage: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Vérifier le statut du cache
     */
    private function checkCacheStatus(): bool
    {
        try {
            cache()->put('test_key', 'test_value', 1);
            $value = cache()->get('test_key');
            cache()->forget('test_key');
            
            return $value === 'test_value';
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Vérifier le statut de la queue
     */
    private function checkQueueStatus(): bool
    {
        try {
            // Vérifier si les queues sont configurées
            $driver = config('queue.default');
            
            if ($driver === 'sync') {
                return true; // Sync est toujours "disponible"
            }
            
            // Pour Redis ou database, on pourrait faire des vérifications plus poussées
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
