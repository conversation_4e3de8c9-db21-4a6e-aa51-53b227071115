<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Service pour surveiller et optimiser les performances du chatbot
 */
class ChatbotPerformanceService
{
    /**
     * Enregistrer une métrique de performance
     */
    public static function recordMetric(string $operation, float $duration, bool $success = true): void
    {
        $timestamp = now()->timestamp;
        $key = "chatbot_metrics_{$operation}";
        
        $metric = [
            'timestamp' => $timestamp,
            'duration' => $duration,
            'success' => $success,
        ];
        
        // Stocker les métriques des dernières 24h
        $metrics = Cache::get($key, []);
        $metrics[] = $metric;
        
        // Garder seulement les métriques des dernières 24h
        $oneDayAgo = $timestamp - 86400;
        $metrics = array_filter($metrics, fn($m) => $m['timestamp'] > $oneDayAgo);
        
        Cache::put($key, $metrics, 86400); // 24h
        
        // Log si la performance est dégradée
        if ($duration > self::getThreshold($operation)) {
            Log::warning("Performance dégradée pour {$operation}: {$duration}s");
        }
    }
    
    /**
     * Obtenir les statistiques de performance
     */
    public static function getStats(string $operation = null): array
    {
        if ($operation) {
            return self::getOperationStats($operation);
        }
        
        $operations = ['query', 'health', 'ollama_filter', 'ollama_compare'];
        $stats = [];
        
        foreach ($operations as $op) {
            $stats[$op] = self::getOperationStats($op);
        }
        
        return $stats;
    }
    
    /**
     * Obtenir les statistiques pour une opération spécifique
     */
    private static function getOperationStats(string $operation): array
    {
        $key = "chatbot_metrics_{$operation}";
        $metrics = Cache::get($key, []);
        
        if (empty($metrics)) {
            return [
                'count' => 0,
                'avg_duration' => 0,
                'max_duration' => 0,
                'min_duration' => 0,
                'success_rate' => 0,
                'last_24h' => 0,
            ];
        }
        
        $durations = array_column($metrics, 'duration');
        $successes = array_filter($metrics, fn($m) => $m['success']);
        
        return [
            'count' => count($metrics),
            'avg_duration' => round(array_sum($durations) / count($durations), 3),
            'max_duration' => round(max($durations), 3),
            'min_duration' => round(min($durations), 3),
            'success_rate' => round((count($successes) / count($metrics)) * 100, 2),
            'last_24h' => count($metrics),
        ];
    }
    
    /**
     * Obtenir le seuil de performance pour une opération
     */
    private static function getThreshold(string $operation): float
    {
        $thresholds = [
            'query' => 30.0, // 30 secondes
            'health' => 5.0,  // 5 secondes
            'ollama_filter' => 15.0, // 15 secondes
            'ollama_compare' => 20.0, // 20 secondes
        ];
        
        return $thresholds[$operation] ?? 10.0;
    }
    
    /**
     * Vérifier si le système est surchargé
     */
    public static function isSystemOverloaded(): bool
    {
        $activeRequests = Cache::get('chatbot_active_requests', 0);
        $maxConcurrent = config('chatbot.performance.max_concurrent_requests', 5);
        
        return $activeRequests >= $maxConcurrent;
    }
    
    /**
     * Incrémenter le compteur de requêtes actives
     */
    public static function incrementActiveRequests(): void
    {
        $current = Cache::get('chatbot_active_requests', 0);
        Cache::put('chatbot_active_requests', $current + 1, 300); // 5 minutes
    }
    
    /**
     * Décrémenter le compteur de requêtes actives
     */
    public static function decrementActiveRequests(): void
    {
        $current = Cache::get('chatbot_active_requests', 0);
        if ($current > 0) {
            Cache::put('chatbot_active_requests', $current - 1, 300);
        }
    }
    
    /**
     * Obtenir les recommandations d'optimisation
     */
    public static function getOptimizationRecommendations(): array
    {
        $stats = self::getStats();
        $recommendations = [];
        
        foreach ($stats as $operation => $data) {
            if ($data['count'] > 0) {
                // Vérifier la durée moyenne
                if ($data['avg_duration'] > self::getThreshold($operation) * 0.8) {
                    $recommendations[] = [
                        'type' => 'performance',
                        'operation' => $operation,
                        'message' => "Durée moyenne élevée pour {$operation}: {$data['avg_duration']}s",
                        'suggestion' => self::getSuggestion($operation, 'duration')
                    ];
                }
                
                // Vérifier le taux de succès
                if ($data['success_rate'] < 95) {
                    $recommendations[] = [
                        'type' => 'reliability',
                        'operation' => $operation,
                        'message' => "Taux de succès faible pour {$operation}: {$data['success_rate']}%",
                        'suggestion' => self::getSuggestion($operation, 'success_rate')
                    ];
                }
            }
        }
        
        return $recommendations;
    }
    
    /**
     * Obtenir une suggestion d'optimisation
     */
    private static function getSuggestion(string $operation, string $issue): string
    {
        $suggestions = [
            'query' => [
                'duration' => 'Considérez activer le traitement asynchrone ou réduire le timeout',
                'success_rate' => 'Vérifiez la connectivité avec l\'API FastAPI'
            ],
            'health' => [
                'duration' => 'Réduisez le timeout de health check',
                'success_rate' => 'Vérifiez que le service chatbot est démarré'
            ],
            'ollama_filter' => [
                'duration' => 'Optimisez les paramètres Ollama (température, max_tokens)',
                'success_rate' => 'Vérifiez la connectivité avec Ollama'
            ],
            'ollama_compare' => [
                'duration' => 'Réduisez la complexité des prompts de comparaison',
                'success_rate' => 'Vérifiez la configuration du modèle Ollama'
            ]
        ];
        
        return $suggestions[$operation][$issue] ?? 'Consultez les logs pour plus de détails';
    }
}
